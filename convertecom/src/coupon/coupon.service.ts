import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Coupon } from './coupon.entity';
import { DeepPartial, EntityManager, MoreThan } from 'typeorm';
import { Tenant } from '../tenant/tenant.entity';
import { first, sampleSize } from 'lodash';
import { FindConditions } from 'typeorm/find-options/FindConditions';
import { ObjectLiteral } from 'typeorm/common/ObjectLiteral';
import { CouponRepository } from './coupon.repository';

@Injectable()
export class CouponService {
  private readonly log = new Logger(CouponService.name, true);

  constructor(
    @InjectRepository(CouponRepository)
    private readonly couponRepository: CouponRepository,
  ) { }

  async generateCode(tenant: Tenant) {
    const availableCharacters =
      process.env.AVAILABLE_CODE_CHARACTERS || 'ABCDEFGHJKLMNPQRTUVWXY346789'; // TODO maybe this should be tenant-configurable?
    return 'CONVERTECOM_' + sampleSize(availableCharacters, 10).join('');
  }

  async calculateCouponAmount(coupon: DeepPartial<Coupon>): Promise<number> {
    // TODO advanced logic. min/max/etc

    // No special rules
    return (
      coupon.tenant.couponPercentage * coupon.originatorOrder.subtotalPrice
    );
  }

  async findAll(
    conditions:
      | Array<FindConditions<Coupon>>
      | FindConditions<Coupon>
      | ObjectLiteral
      | string,
  ): Promise<Coupon[]> {
    return this.couponRepository.find({ where: conditions });
  }

  async create(coupon: DeepPartial<Coupon>, tenant: Tenant): Promise<Coupon> {
    coupon.tenant = tenant;

    coupon.percentage = tenant.couponPercentage;
    coupon.couponAmount = await this.calculateCouponAmount(coupon);

    coupon.code = await this.generateCode(tenant);

    return this.couponRepository.save(coupon);
  }

  async createFallbackCoupon(
    tenant: Tenant,
    entityManager?: EntityManager,
  ): Promise<Coupon> {
    const code = await this.generateCode(tenant);
    const coupon: DeepPartial<Coupon> = {
      isFallback: true,
      isDisabled: false,
      tenant,
      couponAmount: 0,
      percentage: 0.05,
      code,
    };
    if (entityManager) {
      return await entityManager.getRepository(Coupon).save(coupon);
    }
    return await this.couponRepository.save(coupon);
  }

  async getTenantFallbackCoupon(tenant: Tenant): Promise<Coupon> {
    return await this.couponRepository.findOne({
      where: { tenant, isFallback: true },
    });
  }

  async findReservedCoupon(
    reservedId: string | number,
    tenant: Tenant,
    includeExpired = false,
  ): Promise<Coupon> {
    const where: any = {
      tenantId: tenant.id,
      reservedId,
    };

    if (!includeExpired) {
      where.reservedUntil = MoreThan('NOW()');
    }

    const coupon = await this.couponRepository.findOne({
      where,
      order: { reservedUntil: 'DESC' }, // In case user has redeemed a coupon in the past, return the most recent one
      relations: ['redemptionOrders'],
    });

    if (!coupon || coupon.redemptionOrders.length > 0) {
      // Treat a redeemed coupon as non-existent
      // TODO maybe tenants will want a config param for whether the same user id should be able to redeem two separate coupons?
      return null;
    }

    return coupon;
  }

  async getOrUpdateReservedCoupon(
    reservedId: string | number,
    cartAmount: number,
    tenant: Tenant,
  ): Promise<any> {
    // If this user already has a coupon reserved, use that
    const existingCoupon = await this.findReservedCoupon(reservedId, tenant);

    const minimumCouponAmount =
      tenant.fallbackCoupon && !!tenant.fallbackCoupon.percentage
        ? (tenant.fallbackCoupon.percentage * cartAmount).toFixed(2)
        : 0;
    const maximumCouponAmount =
      tenant.fallbackCouponMaxPercentage && !!tenant.fallbackCouponMaxPercentage
        ? (tenant.fallbackCouponMaxPercentage * cartAmount).toFixed(2)
        : 0;

    if (existingCoupon) {
      let shouldReleaseCoupon = false;
      /*
      Ensure the customer's cart isn't below the minimum amount for the coupon.
       */
      if (existingCoupon.minimumCartSubtotal > cartAmount) {
        this.log.log(
          `User ${reservedId} is no longer qualified for coupon ${existingCoupon.id
          } because a cart of $${cartAmount} is below the minimum cart subtotal of $${existingCoupon.minimumCartSubtotal
          }. Releasing it and reassigning a new one.`,
        );
        shouldReleaseCoupon = true;
      }
      /*
        Ensure the customer is getting at least the fallback coupon discount. i.e. tenant has 5% fallback. If the user has a cart worth $20 and
        receives a $2 coupon (10% discount), then updates their cart to be $100, they now have a 2% discount and
        should receive a new coupon worth at least $5.
        */
      if (
        !!existingCoupon.couponAmount &&
        existingCoupon.couponAmount < minimumCouponAmount
      ) {
        this.log.log(
          `User ${reservedId} is no longer qualified for coupon "${existingCoupon.id
          } because a discount of $${existingCoupon.couponAmount
          } is below the minimum discount of $${minimumCouponAmount}. Releasing it and reassigning a new one.`,
        );
        shouldReleaseCoupon = true;
      }

      // Release and continue to get a new one.
      if (shouldReleaseCoupon) {
        await this.releaseCouponByUserId(reservedId, tenant);
      } else {
        return existingCoupon;
      }
    }

    // Reserve an available coupon
    return await this.couponRepository.reserveCoupon(
      reservedId,
      tenant.reservationMinutes,
      tenant.id,
      cartAmount,
      minimumCouponAmount,
      tenant.fallbackCouponMaxPercentage,
    );
  }

  /**
   * Note that this will allow a user to refresh a coupon that has already been redeemed, but it does require that the "reservedId"
   * values match, so this shouldn't be an issue
   */
  async refreshCouponByUserId(
    reservedId: string | number,
    tenant: Tenant,
  ): Promise<any> {
    const reservedUntilString = `NOW() + INTERVAL '${tenant.reservationMinutes
      } minutes'`;

    const result = await this.couponRepository
      .createQueryBuilder()
      .update(Coupon)
      .set({ reservedUntil: () => reservedUntilString })
      .where('reservedId = :reservedId', { reservedId })
      .andWhere(`"tenantId" = :tenantId`, { tenantId: tenant.id })
      .andWhere('isFallback = false')
      .returning(['code', 'reservedUntil', 'couponAmount'])
      .execute();

    return first(result.raw);
  }

  /**
   *   Note that this will allow a user to release a coupon they've redeemed as long as the reservedId values match, but the "reserve" endpoint
   *   will ignore redeemed coupons, so this shouldn't be an issue
   */
  async releaseCouponByUserId(
    reservedId: string | number,
    tenant: Tenant,
  ): Promise<void> {
    const result = await this.couponRepository
      .createQueryBuilder()
      .update(Coupon)
      .set({ reservedUntil: null, reservedId: null })
      .where('reservedId = :reservedId', { reservedId })
      .andWhere(`"tenantId" = :tenantId`, { tenantId: tenant.id })
      .andWhere('isFallback = false')
      .returning(['code', 'reservedUntil'])
      .execute();

    return first(result.raw);
  }
}
