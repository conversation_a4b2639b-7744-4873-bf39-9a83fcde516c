# Development Environment Configuration

# General
environment = "dev"
aws_region  = "us-east-1"

# Networking
vpc_cidr = "10.0.0.0/16"

# ECS - Development (minimal resources)
ecs_min_capacity              = 1
ecs_max_capacity              = 2
ecs_target_cpu_utilization    = 80  # Higher threshold for dev
ecs_target_memory_utilization = 85

# RDS - Development (minimal)
rds_instance_class         = "db.t3.micro"
rds_allocated_storage      = 20
rds_backup_retention_period = 3  # Shorter retention for dev
rds_multi_az               = false

# Cost Optimization - Development
enable_container_insights = false
enable_nat_gateway       = false
enable_elasticache       = false

# Monitoring - Development (shorter retention)
cloudwatch_log_retention_days = 3

# Auto-scaling - Development (longer cooldowns)
scale_up_cooldown   = 600   # 10 minutes
scale_down_cooldown = 900   # 15 minutes
