# Production Environment Configuration

# General
environment = "prod"
aws_region  = "us-east-1"

# Networking
vpc_cidr = "10.2.0.0/16"

# ECS - Production
ecs_min_capacity              = 1
ecs_max_capacity              = 2
ecs_target_cpu_utilization    = 70
ecs_target_memory_utilization = 80

# RDS - Production (high availability)
rds_instance_class         = "db.t3.small"
rds_allocated_storage      = 100
rds_backup_retention_period = 30  # Longer retention for prod
rds_multi_az               = true  # High availability

# Cost Optimization - Production
enable_container_insights = false  # Keep disabled for cost
enable_nat_gateway       = false
enable_elasticache       = false

# Monitoring - Production
cloudwatch_log_retention_days = 30

# Auto-scaling - Production (responsive)
scale_up_cooldown   = 180  # 3 minutes
scale_down_cooldown = 300  # 5 minutes
