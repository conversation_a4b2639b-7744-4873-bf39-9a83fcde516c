# Staging Environment Configuration

# General
environment = "staging"
aws_region  = "us-east-1"

# Networking
vpc_cidr = "10.1.0.0/16"

# ECS - Staging (production-like)
ecs_min_capacity              = 1
ecs_max_capacity              = 2
ecs_target_cpu_utilization    = 70
ecs_target_memory_utilization = 80

# RDS - Staging (production-like)
rds_instance_class         = "db.t3.small"
rds_allocated_storage      = 50
rds_backup_retention_period = 7
rds_multi_az               = false  # Single AZ for cost

# Cost Optimization - Staging
enable_container_insights = false
enable_nat_gateway       = false
enable_elasticache       = false

# Monitoring - Staging
cloudwatch_log_retention_days = 7

# Auto-scaling - Staging
scale_up_cooldown   = 300  # 5 minutes
scale_down_cooldown = 600  # 10 minutes
