variable "services" {
  description = "Map of services and their ECR repository configurations"
  type = map(object({
    service_name     = string
    repository_name  = string
  }))
  default = {
    convertecom = {
      service_name    = "convertecom"
      repository_name = "convertecom"
    }
    admin_master = {
      service_name    = "convertecom-admin-master"
      repository_name = "convertecom-admin-master"
    }
    admin_ui = {
      service_name    = "convertecom-admin-ui"
      repository_name = "convertecom-admin-ui"
    }
    queue_master = {
      service_name    = "convertecom-queue-master"
      repository_name = "convertecom-queue-master"
    }
    billing_service = {
      service_name    = "convertecom-billing-service-master"
      repository_name = "convertecom-billing-service-master"
    }
  }
}

variable "tags" {
  description = "Common tags to apply to all ECR resources"
  type        = map(string)
  default     = {}
}

variable "enable_cross_account_access" {
  description = "Enable cross-account access to ECR repositories"
  type        = bool
  default     = false
}

variable "allowed_account_ids" {
  description = "List of AWS account IDs allowed to access ECR repositories"
  type        = list(string)
  default     = []
}
