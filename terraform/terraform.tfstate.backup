{"version": 4, "terraform_version": "1.11.4", "serial": 47, "lineage": "f707ed3a-6003-7c47-378b-83fdf5771621", "outputs": {"alb_arn": {"value": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/convertecom-prod-alb/2b76a17fd8ec80d8", "type": "string"}, "alb_dns_name": {"value": "convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com", "type": "string"}, "alb_security_group_id": {"value": "sg-0b4a390d48f78d36a", "type": "string"}, "alb_target_group_arns": {"value": {"admin-api": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-api-tg/75a77f694242f09e", "admin-ui": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-ui-tg/efaafac524925211", "billing": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-billing-tg/f7fe1d4d35f295fd", "convertecom": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-convertecom-tg/79f04666ddc872db", "queue": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-queue-tg/57b339c67b20ef40"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "alb_zone_id": {"value": "Z35SXDOTRQ7X7K", "type": "string"}, "application_urls": {"value": {"admin_api": "https://convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com/admin-api", "admin_ui": "https://convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com/admin", "billing_api": "https://convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com/billing", "main_api": "https://convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com/api", "queue_api": "https://convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com/queue"}, "type": ["object", {"admin_api": "string", "admin_ui": "string", "billing_api": "string", "main_api": "string", "queue_api": "string"}]}, "database_name": {"value": "convertecom", "type": "string"}, "database_url_parameter_name": {"value": "/convertecom/prod/database-url", "type": "string"}, "ecs_cluster_id": {"value": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "type": "string"}, "ecs_cluster_name": {"value": "convertecom-prod-cluster", "type": "string"}, "ecs_security_group_id": {"value": "sg-09beece4267422113", "type": "string"}, "ecs_service_names": {"value": {"admin-api": "convertecom-prod-admin-api", "admin-ui": "convertecom-prod-admin-ui", "billing": "convertecom-prod-billing", "convertecom": "convertecom-prod-convertecom", "queue": "convertecom-prod-queue"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "ecs_task_execution_role_arn": {"value": "arn:aws:iam::************:role/convertecom-prod-ecs-task-execution-role", "type": "string"}, "ecs_task_role_arn": {"value": "arn:aws:iam::************:role/convertecom-prod-ecs-task-role", "type": "string"}, "estimated_monthly_cost": {"value": {"alb": "~$16", "cloudwatch": "~$3-5", "data_transfer": "~$5-10", "ecs_fargate": "~$25-50 (5 services, 1-2 tasks each)", "parameter_store": "~$1", "rds_micro": "~$12", "total_estimate": "~$62-94"}, "type": ["object", {"alb": "string", "cloudwatch": "string", "data_transfer": "string", "ecs_fargate": "string", "parameter_store": "string", "rds_micro": "string", "total_estimate": "string"}]}, "parameter_store_names": {"value": {"database_url": "/convertecom/prod/database-url", "jwt_secret": "/convertecom/prod/jwt-secret", "new_relic_license": "/convertecom/prod/new-relic-license", "sentry_dsn": "/convertecom/prod/sentry-dsn", "shopify_api_key": "/convertecom/prod/shopify-api-key", "shopify_api_secret": "/convertecom/prod/shopify-api-secret"}, "type": ["object", {"database_url": "string", "jwt_secret": "string", "new_relic_license": "string", "sentry_dsn": "string", "shopify_api_key": "string", "shopify_api_secret": "string"}]}, "private_subnet_ids": {"value": ["subnet-0863bbeb22e6d9a0c", "subnet-00e956cf832de8f71"], "type": ["tuple", ["string", "string"]]}, "public_subnet_ids": {"value": ["subnet-0482986df4ee341cf", "subnet-0167267bc30dac957"], "type": ["tuple", ["string", "string"]]}, "rds_security_group_id": {"value": "sg-03f751bf8bed04907", "type": "string"}, "vpc_id": {"value": "vpc-0e8ea51886de5a37f", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az6", "use1-az1", "use1-az2", "use1-az4", "use1-az3", "use1-az5"]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/mathi-convertecom", "id": "************", "user_id": "AIDAYH2GE3MOCDWBALLYS"}, "sensitive_attributes": []}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/convertecom-prod-alb/2b76a17fd8ec80d8", "arn_suffix": "app/convertecom-prod-alb/2b76a17fd8ec80d8", "client_keep_alive": 3600, "connection_logs": [{"bucket": "", "enabled": false, "prefix": ""}], "customer_owned_ipv4_pool": "", "desync_mitigation_mode": "defensive", "dns_name": "convertecom-prod-alb-**********.us-east-1.elb.amazonaws.com", "dns_record_client_routing_policy": null, "drop_invalid_header_fields": false, "enable_cross_zone_load_balancing": true, "enable_deletion_protection": false, "enable_http2": true, "enable_tls_version_and_cipher_suite_headers": false, "enable_waf_fail_open": false, "enable_xff_client_port": false, "enable_zonal_shift": false, "enforce_security_group_inbound_rules_on_private_link_traffic": "", "id": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/convertecom-prod-alb/2b76a17fd8ec80d8", "idle_timeout": 60, "internal": false, "ip_address_type": "ipv4", "ipam_pools": [], "load_balancer_type": "application", "minimum_load_balancer_capacity": [], "name": "convertecom-prod-alb", "name_prefix": "", "preserve_host_header": false, "security_groups": ["sg-0b4a390d48f78d36a"], "subnet_mapping": [{"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0167267bc30dac957"}, {"allocation_id": "", "ipv6_address": "", "outpost_id": "", "private_ipv4_address": "", "subnet_id": "subnet-0482986df4ee341cf"}], "subnets": ["subnet-0167267bc30dac957", "subnet-0482986df4ee341cf"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-alb", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-alb", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f", "xff_header_processing_mode": "append", "zone_id": "Z35SXDOTRQ7X7K"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener", "name": "http", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"alpn_policy": null, "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "certificate_arn": null, "default_action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [{"content_type": "text/plain", "message_body": "Service not found", "status_code": "404"}], "forward": [], "order": 1, "redirect": [], "target_group_arn": "", "type": "fixed-response"}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "load_balancer_arn": "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/convertecom-prod-alb/2b76a17fd8ec80d8", "mutual_authentication": [], "port": 80, "protocol": "HTTP", "routing_http_request_x_amzn_mtls_clientcert_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_issuer_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_leaf_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_serial_number_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_subject_header_name": null, "routing_http_request_x_amzn_mtls_clientcert_validity_header_name": null, "routing_http_request_x_amzn_tls_cipher_suite_header_name": null, "routing_http_request_x_amzn_tls_version_header_name": null, "routing_http_response_access_control_allow_credentials_header_value": "", "routing_http_response_access_control_allow_headers_header_value": "", "routing_http_response_access_control_allow_methods_header_value": "", "routing_http_response_access_control_allow_origin_header_value": "", "routing_http_response_access_control_expose_headers_header_value": "", "routing_http_response_access_control_max_age_header_value": "", "routing_http_response_content_security_policy_header_value": "", "routing_http_response_server_enabled": true, "routing_http_response_strict_transport_security_header_value": "", "routing_http_response_x_content_type_options_header_value": "", "routing_http_response_x_frame_options_header_value": "", "ssl_policy": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tcp_idle_timeout_seconds": null, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener", "name": "https", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener_rule", "name": "http_services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-api-tg/75a77f694242f09e", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/b8808675b1e86e28", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/admin-api/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/b8808675b1e86e28", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "priority": 100, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-master"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.http", "module.alb.aws_lb_target_group.services", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-ui-tg/efaafac524925211", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/4e6d94b48014f1c9", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/admin/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/4e6d94b48014f1c9", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "priority": 200, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-ui"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-ui"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.http", "module.alb.aws_lb_target_group.services", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}, {"index_key": "billing", "schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-billing-tg/f7fe1d4d35f295fd", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/c9711ab1af854f6c", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/billing/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/c9711ab1af854f6c", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "priority": 500, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-billing-service-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-billing-service-master"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.http", "module.alb.aws_lb_target_group.services", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-convertecom-tg/79f04666ddc872db", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/0ceb306b2e454e65", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/api/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/0ceb306b2e454e65", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "priority": 300, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.http", "module.alb.aws_lb_target_group.services", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}, {"index_key": "queue", "schema_version": 0, "attributes": {"action": [{"authenticate_cognito": [], "authenticate_oidc": [], "fixed_response": [], "forward": [], "order": 1, "redirect": [], "target_group_arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-queue-tg/57b339c67b20ef40", "type": "forward"}], "arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/0dc97dff0a228df1", "condition": [{"host_header": [], "http_header": [], "http_request_method": [], "path_pattern": [{"values": ["/queue/*"]}], "query_string": [], "source_ip": []}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:listener-rule/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8/0dc97dff0a228df1", "listener_arn": "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/convertecom-prod-alb/2b76a17fd8ec80d8/e5729719ffb597d8", "priority": 400, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-queue-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-queue-master"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.alb.aws_lb.main", "module.alb.aws_lb_listener.http", "module.alb.aws_lb_target_group.services", "module.networking.aws_security_group.alb", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}]}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_listener_rule", "name": "https_services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.alb", "mode": "managed", "type": "aws_lb_target_group", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-api-tg/75a77f694242f09e", "arn_suffix": "targetgroup/convertecom-prod-admin-api-tg/75a77f694242f09e", "connection_termination": null, "deregistration_delay": "30", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-api-tg/75a77f694242f09e", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": [], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "convertecom-prod-admin-api-tg", "name_prefix": "", "port": 3000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-admin-api-tg", "Project": "convertecom", "Service": "convertecom-admin-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-admin-api-tg", "Project": "convertecom", "Service": "convertecom-admin-master"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-ui-tg/efaafac524925211", "arn_suffix": "targetgroup/convertecom-prod-admin-ui-tg/efaafac524925211", "connection_termination": null, "deregistration_delay": "30", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-admin-ui-tg/efaafac524925211", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": [], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "convertecom-prod-admin-ui-tg", "name_prefix": "", "port": 8080, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-admin-ui-tg", "Project": "convertecom", "Service": "convertecom-admin-ui"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-admin-ui-tg", "Project": "convertecom", "Service": "convertecom-admin-ui"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-billing-tg/f7fe1d4d35f295fd", "arn_suffix": "targetgroup/convertecom-prod-billing-tg/f7fe1d4d35f295fd", "connection_termination": null, "deregistration_delay": "30", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-billing-tg/f7fe1d4d35f295fd", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": [], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "convertecom-prod-billing-tg", "name_prefix": "", "port": 3000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-billing-tg", "Project": "convertecom", "Service": "convertecom-billing-service-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-billing-tg", "Project": "convertecom", "Service": "convertecom-billing-service-master"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-convertecom-tg/79f04666ddc872db", "arn_suffix": "targetgroup/convertecom-prod-convertecom-tg/79f04666ddc872db", "connection_termination": null, "deregistration_delay": "30", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-convertecom-tg/79f04666ddc872db", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": [], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "convertecom-prod-convertecom-tg", "name_prefix": "", "port": 3000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-convertecom-tg", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-convertecom-tg", "Project": "convertecom", "Service": "convertecom"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-queue-tg/57b339c67b20ef40", "arn_suffix": "targetgroup/convertecom-prod-queue-tg/57b339c67b20ef40", "connection_termination": null, "deregistration_delay": "30", "health_check": [{"enabled": true, "healthy_threshold": 2, "interval": 30, "matcher": "200", "path": "/health", "port": "traffic-port", "protocol": "HTTP", "timeout": 5, "unhealthy_threshold": 2}], "id": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/convertecom-prod-queue-tg/57b339c67b20ef40", "ip_address_type": "ipv4", "lambda_multi_value_headers_enabled": false, "load_balancer_arns": [], "load_balancing_algorithm_type": "round_robin", "load_balancing_anomaly_mitigation": "off", "load_balancing_cross_zone_enabled": "use_load_balancer_configuration", "name": "convertecom-prod-queue-tg", "name_prefix": "", "port": 8000, "preserve_client_ip": null, "protocol": "HTTP", "protocol_version": "HTTP1", "proxy_protocol_v2": false, "slow_start": 0, "stickiness": [{"cookie_duration": 86400, "cookie_name": "", "enabled": false, "type": "lb_cookie"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-queue-tg", "Project": "convertecom", "Service": "convertecom-queue-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-queue-tg", "Project": "convertecom", "Service": "convertecom-queue-master"}, "target_failover": [{"on_deregistration": null, "on_unhealthy": null}], "target_group_health": [{"dns_failover": [{"minimum_healthy_targets_count": "1", "minimum_healthy_targets_percentage": "off"}], "unhealthy_state_routing": [{"minimum_healthy_targets_count": 1, "minimum_healthy_targets_percentage": "off"}]}], "target_health_state": [{"enable_unhealthy_connection_termination": null, "unhealthy_draining_interval": null}], "target_type": "ip", "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.ecs", "mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1"}, "sensitive_attributes": []}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/convertecom-prod/admin-api", "id": "/ecs/convertecom-prod/admin-api", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/convertecom-prod/admin-api", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-master"}}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/convertecom-prod/admin-ui", "id": "/ecs/convertecom-prod/admin-ui", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/convertecom-prod/admin-ui", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-ui"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-ui"}}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/convertecom-prod/billing", "id": "/ecs/convertecom-prod/billing", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/convertecom-prod/billing", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-billing-service-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-billing-service-master"}}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/convertecom-prod/convertecom", "id": "/ecs/convertecom-prod/convertecom", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/convertecom-prod/convertecom", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom"}}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/ecs/convertecom-prod/queue", "id": "/ecs/convertecom-prod/queue", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/convertecom-prod/queue", "name_prefix": "", "retention_in_days": 30, "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-queue-master"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-queue-master"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.ecs", "mode": "managed", "type": "aws_ecs_cluster", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "configuration": [], "id": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "name": "convertecom-prod-cluster", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "disabled"}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.iam", "mode": "data", "type": "aws_iam_policy_document", "name": "ecs_task_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"ecs-tasks.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["ecs-tasks.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.iam", "mode": "data", "type": "aws_iam_policy_document", "name": "ecs_task_execution_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"ecs-tasks.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["ecs-tasks.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.iam", "mode": "data", "type": "aws_iam_policy_document", "name": "ecs_task_execution_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "952563030", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ecr:GetDownloadUrlForLayer\",\n        \"ecr:GetAuthorizationToken\",\n        \"ecr:BatchGetImage\",\n        \"ecr:BatchCheckLayerAvailability\"\n      ],\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"logs:PutLogEvents\",\n        \"logs:DescribeLogStreams\",\n        \"logs:CreateLogStream\",\n        \"logs:CreateLogGroup\"\n      ],\n      \"Resource\": \"arn:aws:logs:*:*:*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ssm:GetParametersByPath\",\n        \"ssm:GetParameters\",\n        \"ssm:GetParameter\"\n      ],\n      \"Resource\": \"arn:aws:ssm:*:*:parameter/convertecom/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"secretsmanager:GetSecretValue\",\n      \"Resource\": \"arn:aws:secretsmanager:*:*:secret:convertecom/*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"ecr:GetDownloadUrlForLayer\",\"ecr:GetAuthorizationToken\",\"ecr:BatchGetImage\",\"ecr:BatchCheckLayerAvailability\"],\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"logs:PutLogEvents\",\"logs:DescribeLogStreams\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Effect\":\"Allow\",\"Action\":[\"ssm:GetParametersByPath\",\"ssm:GetParameters\",\"ssm:GetParameter\"],\"Resource\":\"arn:aws:ssm:*:*:parameter/convertecom/*\"},{\"Effect\":\"Allow\",\"Action\":\"secretsmanager:GetSecretValue\",\"Resource\":\"arn:aws:secretsmanager:*:*:secret:convertecom/*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetAuthorizationToken", "ecr:GetDownloadUrlForLayer"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:DescribeLogStreams", "logs:PutLogEvents"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:logs:*:*:*"], "sid": ""}, {"actions": ["ssm:GetParameter", "ssm:GetParameters", "ssm:GetParametersByPath"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:ssm:*:*:parameter/convertecom/*"], "sid": ""}, {"actions": ["secretsmanager:GetSecretValue"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:secretsmanager:*:*:secret:convertecom/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.iam", "mode": "data", "type": "aws_iam_policy_document", "name": "ecs_task_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ssm:GetParametersByPath\",\n        \"ssm:GetParameters\",\n        \"ssm:GetParameter\"\n      ],\n      \"Resource\": \"arn:aws:ssm:*:*:parameter/convertecom/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"cloudwatch:PutMetricData\",\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::convertecom-*/*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ses:SendRawEmail\",\n        \"ses:SendEmail\"\n      ],\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"ssm:GetParametersByPath\",\"ssm:GetParameters\",\"ssm:GetParameter\"],\"Resource\":\"arn:aws:ssm:*:*:parameter/convertecom/*\"},{\"Effect\":\"Allow\",\"Action\":\"cloudwatch:PutMetricData\",\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":\"arn:aws:s3:::convertecom-*/*\"},{\"Effect\":\"Allow\",\"Action\":[\"ses:SendRawEmail\",\"ses:SendEmail\"],\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["ssm:GetParameter", "ssm:GetParameters", "ssm:GetParametersByPath"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:ssm:*:*:parameter/convertecom/*"], "sid": ""}, {"actions": ["cloudwatch:PutMetricData"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["s3:DeleteObject", "s3:GetObject", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::convertecom-*/*"], "sid": ""}, {"actions": ["ses:SendEmail", "ses:SendRawEmail"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role", "name": "ecs_autoscaling_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/convertecom-prod-ecs-autoscaling-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"application-autoscaling.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T06:38:09Z", "description": "", "force_detach_policies": false, "id": "convertecom-prod-ecs-autoscaling-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "convertecom-prod-ecs-autoscaling-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "unique_id": "AROAYH2GE3MOGEKT22HMC"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/convertecom-prod-ecs-task-execution-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T06:38:09Z", "description": "", "force_detach_policies": false, "id": "convertecom-prod-ecs-task-execution-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "convertecom-prod-ecs-task-execution-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "unique_id": "AROAYH2GE3MOCQBGWMH74"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.iam.data.aws_iam_policy_document.ecs_task_execution_assume_role"]}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role", "name": "ecs_task_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/convertecom-prod-ecs-task-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-02T06:38:09Z", "description": "", "force_detach_policies": false, "id": "convertecom-prod-ecs-task-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "convertecom-prod-ecs-task-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "unique_id": "AROAYH2GE3MOCLB6RPINA"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.iam.data.aws_iam_policy_document.ecs_task_assume_role"]}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_task_execution_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "convertecom-prod-ecs-task-execution-role:convertecom-prod-ecs-task-execution-policy", "name": "convertecom-prod-ecs-task-execution-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ecr:GetDownloadUrlForLayer\",\"ecr:GetAuthorizationToken\",\"ecr:BatchGetImage\",\"ecr:BatchCheckLayerAvailability\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"logs:PutLogEvents\",\"logs:DescribeLogStreams\",\"logs:CreateLogStream\",\"logs:CreateLogGroup\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"ssm:GetParametersByPath\",\"ssm:GetParameters\",\"ssm:GetParameter\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ssm:*:*:parameter/convertecom/*\"},{\"Action\":\"secretsmanager:GetSecretValue\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:*:*:secret:convertecom/*\"}]}", "role": "convertecom-prod-ecs-task-execution-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.iam.aws_iam_role.ecs_task_execution_role", "module.iam.data.aws_iam_policy_document.ecs_task_execution_assume_role", "module.iam.data.aws_iam_policy_document.ecs_task_execution_policy"]}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_task_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "convertecom-prod-ecs-task-role:convertecom-prod-ecs-task-policy", "name": "convertecom-prod-ecs-task-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssm:GetParametersByPath\",\"ssm:GetParameters\",\"ssm:GetParameter\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:ssm:*:*:parameter/convertecom/*\"},{\"Action\":\"cloudwatch:PutMetricData\",\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"s3:PutObject\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::convertecom-*/*\"},{\"Action\":[\"ses:SendRawEmail\",\"ses:SendEmail\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "convertecom-prod-ecs-task-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.iam.aws_iam_role.ecs_task_role", "module.iam.data.aws_iam_policy_document.ecs_task_assume_role", "module.iam.data.aws_iam_policy_document.ecs_task_policy"]}]}, {"module": "module.iam", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "convertecom-prod-ecs-task-execution-role-20250702063810871100000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "convertecom-prod-ecs-task-execution-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.iam.aws_iam_role.ecs_task_execution_role", "module.iam.data.aws_iam_policy_document.ecs_task_execution_assume_role"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_db_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:subgrp:convertecom-prod-db-subnet-group", "description": "Managed by Terraform", "id": "convertecom-prod-db-subnet-group", "name": "convertecom-prod-db-subnet-group", "name_prefix": "", "subnet_ids": ["subnet-00e956cf832de8f71", "subnet-0863bbeb22e6d9a0c"], "supported_network_types": ["IPV4"], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-db-subnet-group", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-db-subnet-group", "Project": "convertecom"}, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_subnet.private", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-0119c10cf123cfe3b", "id": "igw-0119c10cf123cfe3b", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-igw", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-igw", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0ec75f4100e08378a", "id": "rtb-0ec75f4100e08378a", "owner_id": "************", "propagating_vgws": [], "route": [], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-rt", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-rt", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-02d8a3886b447abe8", "id": "rtb-02d8a3886b447abe8", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0119c10cf123cfe3b", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-rt", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-rt", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["module.networking.aws_internet_gateway.main", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0ab37af885d72975b", "route_table_id": "rtb-0ec75f4100e08378a", "subnet_id": "subnet-0863bbeb22e6d9a0c", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_route_table.private", "module.networking.aws_subnet.private", "module.networking.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-08578a252f6e81ad0", "route_table_id": "rtb-0ec75f4100e08378a", "subnet_id": "subnet-00e956cf832de8f71", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_route_table.private", "module.networking.aws_subnet.private", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-03ae958fee3520f52", "route_table_id": "rtb-02d8a3886b447abe8", "subnet_id": "subnet-0482986df4ee341cf", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_internet_gateway.main", "module.networking.aws_route_table.public", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0bdb0922843512312", "route_table_id": "rtb-02d8a3886b447abe8", "subnet_id": "subnet-0167267bc30dac957", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_internet_gateway.main", "module.networking.aws_route_table.public", "module.networking.aws_subnet.public", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_security_group", "name": "alb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0b4a390d48f78d36a", "description": "Security group for Application Load Balancer", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0b4a390d48f78d36a", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "HTTP", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}, {"cidr_blocks": ["0.0.0.0/0"], "description": "HTTPS", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 443}], "name": "convertecom-prod-alb-20250702063824062500000003", "name_prefix": "convertecom-prod-alb-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-alb-sg", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-alb-sg", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.networking.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.networking", "mode": "managed", "type": "aws_security_group", "name": "ecs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-09beece4267422113", "description": "Security group for ECS tasks", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-09beece4267422113", "ingress": [{"cidr_blocks": [], "description": "Traffic from ALB", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0b4a390d48f78d36a"], "self": false, "to_port": 65535}], "name": "convertecom-prod-ecs-20250702063829474600000005", "name_prefix": "convertecom-prod-ecs-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-ecs-sg", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-ecs-sg", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.networking.aws_security_group.alb", "module.networking.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.networking", "mode": "managed", "type": "aws_security_group", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-03f751bf8bed04907", "description": "Security group for RDS database", "egress": [], "id": "sg-03f751bf8bed04907", "ingress": [{"cidr_blocks": [], "description": "PostgreSQL from ECS", "from_port": 5432, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-09beece4267422113"], "self": false, "to_port": 5432}], "name": "convertecom-prod-rds-20250702063834278100000006", "name_prefix": "convertecom-prod-rds-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-rds-sg", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-rds-sg", "Project": "convertecom"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.networking.aws_security_group.alb", "module.networking.aws_security_group.ecs", "module.networking.aws_vpc.main"], "create_before_destroy": true}]}, {"module": "module.networking", "mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0863bbeb22e6d9a0c", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az6", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0863bbeb22e6d9a0c", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-subnet-1", "Project": "convertecom", "Type": "Private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-subnet-1", "Project": "convertecom", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-00e956cf832de8f71", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-00e956cf832de8f71", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-subnet-2", "Project": "convertecom", "Type": "Private"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-private-subnet-2", "Project": "convertecom", "Type": "Private"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0482986df4ee341cf", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az6", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0482986df4ee341cf", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-subnet-1", "Project": "convertecom", "Type": "Public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-subnet-1", "Project": "convertecom", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0167267bc30dac957", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0167267bc30dac957", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-subnet-2", "Project": "convertecom", "Type": "Public"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-public-subnet-2", "Project": "convertecom", "Type": "Public"}, "timeouts": null, "vpc_id": "vpc-0e8ea51886de5a37f"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.networking.aws_vpc.main"]}]}, {"module": "module.networking", "mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-0e8ea51886de5a37f", "assign_generated_ipv6_cidr_block": false, "cidr_block": "********/16", "default_network_acl_id": "acl-0b3388b57b91e83f9", "default_route_table_id": "rtb-0487f0c66e0fd2f9b", "default_security_group_id": "sg-0d9fc072e153b0a86", "dhcp_options_id": "dopt-0adff13db89d33696", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0e8ea51886de5a37f", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0487f0c66e0fd2f9b", "owner_id": "************", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-vpc", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-vpc", "Project": "convertecom"}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "create_before_destroy": true}]}, {"module": "module.rds", "mode": "data", "type": "aws_subnets", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"filter": [{"name": "tag:Type", "values": ["Private"]}, {"name": "vpc-id", "values": ["vpc-0e8ea51886de5a37f"]}], "id": "us-east-1", "ids": [], "tags": null, "timeouts": null}, "sensitive_attributes": []}]}, {"module": "module.rds", "mode": "managed", "type": "aws_db_parameter_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:us-east-1:************:pg:convertecom-prod-db-params", "description": "Managed by Terraform", "family": "postgres15", "id": "convertecom-prod-db-params", "name": "convertecom-prod-db-params", "name_prefix": "", "parameter": [{"apply_method": "immediate", "name": "log_min_duration_statement", "value": "1000"}, {"apply_method": "immediate", "name": "log_statement", "value": "all"}, {"apply_method": "immediate", "name": "shared_preload_libraries", "value": "pg_stat_statements"}], "skip_destroy": false, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.rds", "mode": "managed", "type": "aws_ssm_parameter", "name": "database_password", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allowed_pattern": "", "arn": "arn:aws:ssm:us-east-1:************:parameter/convertecom/prod/database-password", "data_type": "text", "description": "", "has_value_wo": null, "id": "/convertecom/prod/database-password", "insecure_value": null, "key_id": "alias/aws/ssm", "name": "/convertecom/prod/database-password", "overwrite": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tags_all": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}, "tier": "Standard", "type": "SecureString", "value": "]<l:#>(Zz#sKgJX::{>i6FJM_GyU0:r#", "value_wo": null, "value_wo_version": null, "version": 1}, "sensitive_attributes": [[{"type": "get_attr", "value": "value_wo"}], [{"type": "get_attr", "value": "value"}]], "private": "bnVsbA==", "dependencies": ["module.rds.random_password.database_password"]}]}, {"module": "module.rds", "mode": "managed", "type": "random_password", "name": "database_password", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$9UjOeMP5OOE5Cj1XelUV6.OI6ijUSkgTGB4Bqv4vVkBpUqIrAWiAq", "id": "none", "keepers": null, "length": 32, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "]<l:#>(Zz#sKgJX::{>i6FJM_GyU0:r#", "special": true, "upper": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "bcrypt_hash"}], [{"type": "get_attr", "value": "result"}]]}]}], "check_results": null}