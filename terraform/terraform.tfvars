# Copy this file to terraform.tfvars and update with your values

# General Configuration
aws_region   = "us-east-1"
environment  = "dev"
project_name = "convertecom"

# Networking
vpc_cidr = "10.0.0.0/16"

# ECS Configuration
ecs_min_capacity              = 1
ecs_max_capacity              = 2
ecs_target_cpu_utilization    = 70
ecs_target_memory_utilization = 80

# ECR Repository (update with your account ID)
ecr_repository_url = ""

# RDS Configuration
database_name               = "convertecom"
database_username           = "convertecom"
rds_instance_class         = "db.t3.micro"
rds_allocated_storage      = 20
rds_backup_retention_period = 7
rds_multi_az               = false

# SSL Certificate (create in AWS Certificate Manager first)
ssl_certificate_arn = ""

# Domain (optional)
domain_name = "your-domain.com"

# Sensitive Environment Variables (use AWS Parameter Store or set here)
jwt_secret             = "your-jwt-secret-here"
shopify_api_key        = "your-shopify-api-key"
shopify_api_secret     = "your-shopify-api-secret"
sentry_dsn            = "your-sentry-dsn"
new_relic_license_key = "your-new-relic-license"

# Cost Optimization (keep these false to minimize costs)
enable_container_insights = false
enable_nat_gateway       = false
enable_elasticache       = false

# Monitoring
cloudwatch_log_retention_days = 7

# Auto-scaling Cooldowns
scale_up_cooldown   = 300  # 5 minutes
scale_down_cooldown = 600  # 10 minutes
