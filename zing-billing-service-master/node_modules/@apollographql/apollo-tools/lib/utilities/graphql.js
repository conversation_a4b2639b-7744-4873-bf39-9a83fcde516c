"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const graphql_1 = require("graphql");
function isNode(maybeNode) {
    return maybeNode && typeof maybeNode.kind === "string";
}
exports.isNode = isNode;
function isDocumentNode(node) {
    return isNode(node) && node.kind === graphql_1.Kind.DOCUMENT;
}
exports.isDocumentNode = isDocumentNode;
//# sourceMappingURL=graphql.js.map