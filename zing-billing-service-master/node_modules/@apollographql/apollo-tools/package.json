{"name": "@apollographql/apollo-tools", "version": "0.4.0", "author": "Apollo GraphQL <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-tooling.git"}, "homepage": "https://github.com/apollographql/apollo-tooling", "bugs": "https://github.com/apollographql/apollo-tooling/issues", "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=8", "npm": ">=6"}, "dependencies": {"apollo-env": "0.5.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "setupFiles": ["<rootDir>/../apollo-env/lib/index.js"], "testMatch": null, "testRegex": "/__tests__/.*\\.test\\.(js|ts)$", "testPathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/lib/"], "moduleFileExtensions": ["ts", "js"], "transformIgnorePatterns": ["/node_modules/", "/apollo-env/"], "snapshotSerializers": ["<rootDir>/src/__tests__/snapshotSerializers/astSerializer.ts", "<rootDir>/src/__tests__/snapshotSerializers/graphQLTypeSerializer.ts"], "globals": {"ts-jest": {"tsConfig": "<rootDir>/tsconfig.test.json", "diagnostics": true}}}, "gitHead": "26b5e334784a20f3fd0e0a73c4b280ef55bd5516"}