{"program": {"fileInfos": {"/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es5.d.ts": {"version": "1f753cee573dea40df8a9dc873fef5566957b19ad513874f6643d8dfb14842d0", "signature": "1f753cee573dea40df8a9dc873fef5566957b19ad513874f6643d8dfb14842d0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.d.ts": {"version": "7994d44005046d1413ea31d046577cdda33b8b2470f30281fd9c8b3c99fe2d96", "signature": "7994d44005046d1413ea31d046577cdda33b8b2470f30281fd9c8b3c99fe2d96"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.d.ts": {"version": "5f217838d25704474d9ef93774f04164889169ca31475fe423a9de6758f058d1", "signature": "5f217838d25704474d9ef93774f04164889169ca31475fe423a9de6758f058d1"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.d.ts": {"version": "459097c7bdd88fc5731367e56591e4f465f2c9de81a35427a7bd473165c34743", "signature": "459097c7bdd88fc5731367e56591e4f465f2c9de81a35427a7bd473165c34743"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.core.d.ts": {"version": "2a0390a665763bcc90f0670e43c587928d8fefe2a94a11209c1e22cba7b09f52", "signature": "2a0390a665763bcc90f0670e43c587928d8fefe2a94a11209c1e22cba7b09f52"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.collection.d.ts": {"version": "dd94d8ef48c562389eb58af8df3a3a34d11367f7c818192aa5f16470d469e3f0", "signature": "dd94d8ef48c562389eb58af8df3a3a34d11367f7c818192aa5f16470d469e3f0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.generator.d.ts": {"version": "828413486bdcaa342558e8e4570b1b287b776cb61b4b70b0214bd10c5d9a94c3", "signature": "828413486bdcaa342558e8e4570b1b287b776cb61b4b70b0214bd10c5d9a94c3"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.iterable.d.ts": {"version": "6c9f73334f8bf47aea685ca5b46a8f7e992a223e96fbceb030f26a4d2324ba21", "signature": "6c9f73334f8bf47aea685ca5b46a8f7e992a223e96fbceb030f26a4d2324ba21"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.promise.d.ts": {"version": "e6b8ff2798f8ebd7a1c7afd8671f2cb67ee1901c422f5964d74b0b34c6574ea2", "signature": "e6b8ff2798f8ebd7a1c7afd8671f2cb67ee1901c422f5964d74b0b34c6574ea2"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.proxy.d.ts": {"version": "5e72f949a89717db444e3bd9433468890068bb21a5638d8ab15a1359e05e54fe", "signature": "5e72f949a89717db444e3bd9433468890068bb21a5638d8ab15a1359e05e54fe"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.reflect.d.ts": {"version": "f5b242136ae9bfb1cc99a5971cccc44e99947ae6b5ef6fd8aa54b5ade553b976", "signature": "f5b242136ae9bfb1cc99a5971cccc44e99947ae6b5ef6fd8aa54b5ade553b976"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.d.ts": {"version": "9ae2860252d6b5f16e2026d8a2c2069db7b2a3295e98b6031d01337b96437230", "signature": "9ae2860252d6b5f16e2026d8a2c2069db7b2a3295e98b6031d01337b96437230"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": {"version": "3e0a459888f32b42138d5a39f706ff2d55d500ab1031e0988b5568b0f67c2303", "signature": "3e0a459888f32b42138d5a39f706ff2d55d500ab1031e0988b5568b0f67c2303"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.array.include.d.ts": {"version": "3f96f1e570aedbd97bf818c246727151e873125d0512e4ae904330286c721bc0", "signature": "3f96f1e570aedbd97bf818c246727151e873125d0512e4ae904330286c721bc0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.object.d.ts": {"version": "ff8ad203e83338289b0f5defc1a5b5c253fb7d251b464db497383f915a0df3f4", "signature": "ff8ad203e83338289b0f5defc1a5b5c253fb7d251b464db497383f915a0df3f4"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": {"version": "b8667586a618c5cf64523d4e500ae39e781428abfb28f3de441fc66b56144b6f", "signature": "b8667586a618c5cf64523d4e500ae39e781428abfb28f3de441fc66b56144b6f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.string.d.ts": {"version": "21df2e0059f14dcb4c3a0e125859f6b6ff01332ee24b0065a741d121250bc71c", "signature": "21df2e0059f14dcb4c3a0e125859f6b6ff01332ee24b0065a741d121250bc71c"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.intl.d.ts": {"version": "c1759cb171c7619af0d2234f2f8fb2a871ee88e956e2ed91bb61778e41f272c6", "signature": "c1759cb171c7619af0d2234f2f8fb2a871ee88e956e2ed91bb61778e41f272c6"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": {"version": "28569d59e07d4378cb3d54979c4c60f9f06305c9bb6999ffe6cab758957adc46", "signature": "28569d59e07d4378cb3d54979c4c60f9f06305c9bb6999ffe6cab758957adc46"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts": {"version": "42102aaeb1bfec213be1e9777466a6d551eeba8567fed18c0cdbca8c35e3c6e4", "signature": "42102aaeb1bfec213be1e9777466a6d551eeba8567fed18c0cdbca8c35e3c6e4"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2019.array.d.ts": {"version": "fc7c02b980e7dda0ed14d57ee71705c09efc51ec6286a10b971a25a3bb29262b", "signature": "fc7c02b980e7dda0ed14d57ee71705c09efc51ec6286a10b971a25a3bb29262b"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts": {"version": "ef0e9372b2f8e0afcf521501c1d88a0a32274832bf542d902ac709a9a9699392", "signature": "ef0e9372b2f8e0afcf521501c1d88a0a32274832bf542d902ac709a9a9699392"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts": {"version": "20a63906ba680dc1f9415834efe03d7c318da78ed265152b0aa12f9e0fea9c4e", "signature": "20a63906ba680dc1f9415834efe03d7c318da78ed265152b0aa12f9e0fea9c4e"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts": {"version": "641b9da0622e0225740b5a55f47af9f23f01bf8f4dcbfb81128c16b585900717", "signature": "641b9da0622e0225740b5a55f47af9f23f01bf8f4dcbfb81128c16b585900717"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts": {"version": "92ef6b9f7d41a76e092bd8aa875d5abd8db6068c0d65ef93639393a6f3c57088", "signature": "92ef6b9f7d41a76e092bd8aa875d5abd8db6068c0d65ef93639393a6f3c57088"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts": {"version": "aa92c6e109d0a690456d040b88ca231d5612c6ae77d9250a4286bf3a9d2d0bc6", "signature": "aa92c6e109d0a690456d040b88ca231d5612c6ae77d9250a4286bf3a9d2d0bc6"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts": {"version": "39589f4379be9fc814b5f46f5f49b10a1b4a36f32699b5a209c376239115abc0", "signature": "39589f4379be9fc814b5f46f5f49b10a1b4a36f32699b5a209c376239115abc0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts": {"version": "e24d0f10f33ed2b53f51bbc260889376113282adb687efdb1e7819338ba74899", "signature": "e24d0f10f33ed2b53f51bbc260889376113282adb687efdb1e7819338ba74899"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts": {"version": "5dac29b1f11fe79ef37a180a71926b933d2ae5b654d94eec2921881d66fa8ee2", "signature": "5dac29b1f11fe79ef37a180a71926b933d2ae5b654d94eec2921881d66fa8ee2"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts": {"version": "8a8e1dc812fed250ea827fea7eee9855fa5dbcc337e8cf17056a07e4f3c82063", "signature": "8a8e1dc812fed250ea827fea7eee9855fa5dbcc337e8cf17056a07e4f3c82063"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts": {"version": "dd53e2dde3ca7541e2cd4778899807986de3e1de34a49238898e0e8e5f6fb22e", "signature": "dd53e2dde3ca7541e2cd4778899807986de3e1de34a49238898e0e8e5f6fb22e"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts": {"version": "7bbb657ffe0c15fba4ec761bf9d7d3fffd75549ddf5fae2f822ff1f0713a8f8d", "signature": "7bbb657ffe0c15fba4ec761bf9d7d3fffd75549ddf5fae2f822ff1f0713a8f8d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts": {"version": "ad247139c7b21139a0ef6a0cabdc31bdcf4e56b4c47f9da76cbb9f1b647c0d14", "signature": "ad247139c7b21139a0ef6a0cabdc31bdcf4e56b4c47f9da76cbb9f1b647c0d14"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts": {"version": "6bf090484f8a15c92d719d46806f9da5927c9f10891c649da9b61bc9bf4a7ad4", "signature": "6bf090484f8a15c92d719d46806f9da5927c9f10891c649da9b61bc9bf4a7ad4"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts": {"version": "4e15eb6c72547075f61cdf447cab0bae032ac8ed2da779dfe059a63f47198456", "signature": "4e15eb6c72547075f61cdf447cab0bae032ac8ed2da779dfe059a63f47198456"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts": {"version": "49961c21b5a72d8d0f9dc4790de331c4c4b43dd577bfae3920a7e40eacfdd191", "signature": "49961c21b5a72d8d0f9dc4790de331c4c4b43dd577bfae3920a7e40eacfdd191"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts": {"version": "c2e5f7b34c7d1c257a467d87cb44685c89a2940612816a9001a4201589c2b89f", "signature": "c2e5f7b34c7d1c257a467d87cb44685c89a2940612816a9001a4201589c2b89f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts": {"version": "69bc25843d67239e0bf0eda8aa5f7ba957549bbb7b923a855c816941f20d99ad", "signature": "69bc25843d67239e0bf0eda8aa5f7ba957549bbb7b923a855c816941f20d99ad"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts": {"version": "314896fb688d5e64ce7125aed9b5a101adf6fb65acf44b3c2f04aaa545568936", "signature": "314896fb688d5e64ce7125aed9b5a101adf6fb65acf44b3c2f04aaa545568936"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts": {"version": "501c7fcdbf05156254f7aee8dcd84cd65421473feae0e26a849b66cde7f4fd91", "signature": "501c7fcdbf05156254f7aee8dcd84cd65421473feae0e26a849b66cde7f4fd91"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts": {"version": "9e790340a13027a1283e0e13c2c057c1d35e313c9387ae0e420dffdcefc997c2", "signature": "9e790340a13027a1283e0e13c2c057c1d35e313c9387ae0e420dffdcefc997c2"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts": {"version": "6db9f64ea6999c94634baaa948246466c6c4e3e46d7410ec413d1cbbdb0c2fed", "signature": "6db9f64ea6999c94634baaa948246466c6c4e3e46d7410ec413d1cbbdb0c2fed"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts": {"version": "93e2317ab527e3cd0876ab6b44838876708295f4a63476895febbc1c0ad1c210", "signature": "93e2317ab527e3cd0876ab6b44838876708295f4a63476895febbc1c0ad1c210"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts": {"version": "52702c2843c1a7d9aed010dd90d6e7543c55a03f857c1ddbf0f7fc0e61ddff76", "signature": "52702c2843c1a7d9aed010dd90d6e7543c55a03f857c1ddbf0f7fc0e61ddff76"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts": {"version": "4df3e32a6fd412f208151a389ea6b84e4a4a1269db3b4bfdc94f6738122feaa8", "signature": "4df3e32a6fd412f208151a389ea6b84e4a4a1269db3b4bfdc94f6738122feaa8"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts": {"version": "f07904824e33fc4f8a80139157091f848cde5b58d94639b3b306fa56d6fd15ef", "signature": "f07904824e33fc4f8a80139157091f848cde5b58d94639b3b306fa56d6fd15ef"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts": {"version": "2b7ffaf8581ada8f5560c57832315e176f35fe09483d5be73bb049d55b64c761", "signature": "2b7ffaf8581ada8f5560c57832315e176f35fe09483d5be73bb049d55b64c761"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts": {"version": "e3908488eaef57eb43b2e111e84b6fe71d53d2940c3d643a7472c8cc08a6ef5d", "signature": "e3908488eaef57eb43b2e111e84b6fe71d53d2940c3d643a7472c8cc08a6ef5d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts": {"version": "0f70d0bde6693fb569f7a93d40b53ca72d8b5bc1290da3bd75aabdd83d99ebbd", "signature": "0f70d0bde6693fb569f7a93d40b53ca72d8b5bc1290da3bd75aabdd83d99ebbd"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts": {"version": "495580ddce7c0408099078d3e5f8c0094390cb153822fd65af851c440256c0cd", "signature": "495580ddce7c0408099078d3e5f8c0094390cb153822fd65af851c440256c0cd"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts": {"version": "580a2be87b18995dd03a0369a450140144b3d3b3938a2fea98e5455688282ea6", "signature": "580a2be87b18995dd03a0369a450140144b3d3b3938a2fea98e5455688282ea6"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts": {"version": "b5aedd88da1fb0f65202a3eab3e8b25ce11c0279260b3b3100cf2a2df55c8c7d", "signature": "b5aedd88da1fb0f65202a3eab3e8b25ce11c0279260b3b3100cf2a2df55c8c7d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts": {"version": "9ee62c323e5ebaed0b36eb8f7dc9ce303ac5beae4c5097fc6faab39e05ce0e23", "signature": "9ee62c323e5ebaed0b36eb8f7dc9ce303ac5beae4c5097fc6faab39e05ce0e23"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts": {"version": "578de45eae74c710bf9844ec7edd826ce0246327dc66bafdf5eac1ab02399cfd", "signature": "578de45eae74c710bf9844ec7edd826ce0246327dc66bafdf5eac1ab02399cfd"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts": {"version": "9813974cbc8fb261ddfed74063ac1c474892043d74a559a4c185b08b3861f12a", "signature": "9813974cbc8fb261ddfed74063ac1c474892043d74a559a4c185b08b3861f12a"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts": {"version": "1a22eef002bde659b3a0f21913e5d849e6f6a04f8597e1e5c23a994823d33af7", "signature": "1a22eef002bde659b3a0f21913e5d849e6f6a04f8597e1e5c23a994823d33af7"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts": {"version": "b63ad985ef9c4679f7ec46561067894369918aca0d8266da38af3ca7f0b8aa78", "signature": "b63ad985ef9c4679f7ec46561067894369918aca0d8266da38af3ca7f0b8aa78"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts": {"version": "b234048fdacdab8a8af76cd9e1da7fafdb132a427db515a51474f1ae382353dc", "signature": "b234048fdacdab8a8af76cd9e1da7fafdb132a427db515a51474f1ae382353dc"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts": {"version": "0a69f2387d946843c727f655ddeb95ac7b4c4567007e5a0094586b6b9dff18fb", "signature": "0a69f2387d946843c727f655ddeb95ac7b4c4567007e5a0094586b6b9dff18fb"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts": {"version": "a12f021e504cfd4449a2ca2a57122eee77a45cc9dd5d80f9284d5c6077606b80", "signature": "a12f021e504cfd4449a2ca2a57122eee77a45cc9dd5d80f9284d5c6077606b80"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts": {"version": "d01a662f57973835cc58d3bdde1adc7bbfead0dbbd8898012586fbbc9b349858", "signature": "d01a662f57973835cc58d3bdde1adc7bbfead0dbbd8898012586fbbc9b349858"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts": {"version": "40af2044be77da3a0c0c28c320ab2541f4b45d0f0fc948f43ded9c634a79da4c", "signature": "40af2044be77da3a0c0c28c320ab2541f4b45d0f0fc948f43ded9c634a79da4c"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts": {"version": "97475847a0a6c945b04f70f79d040508b373a32e5b9959400a98b5f41c851232", "signature": "97475847a0a6c945b04f70f79d040508b373a32e5b9959400a98b5f41c851232"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts": {"version": "93488c27ee18d8ef4f49eea805ccc1e52b31cd8e56ebd553be83e41b15df481f", "signature": "93488c27ee18d8ef4f49eea805ccc1e52b31cd8e56ebd553be83e41b15df481f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts": {"version": "3e1fcdd7123aa5c39bcb0d106dd7ac74bad8b5ffdb45685c61baa52019ffa0f5", "signature": "3e1fcdd7123aa5c39bcb0d106dd7ac74bad8b5ffdb45685c61baa52019ffa0f5"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts": {"version": "ef4b00a2eb3145aa70bb327a85d8eff7f4683c681b668a91f3278aaac9df4dd2", "signature": "ef4b00a2eb3145aa70bb327a85d8eff7f4683c681b668a91f3278aaac9df4dd2"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts": {"version": "df652cf6f7971ca1bf1dd7da465f42b28ea36559009a08daf0948d68d7b7879d", "signature": "df652cf6f7971ca1bf1dd7da465f42b28ea36559009a08daf0948d68d7b7879d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts": {"version": "401f29fefb0e952bb40b00040a1f24ba07b7092829ccbc4f3e70a07a1da2e63c", "signature": "401f29fefb0e952bb40b00040a1f24ba07b7092829ccbc4f3e70a07a1da2e63c"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts": {"version": "c6a95fe7eab227efc1deab623691339532797cd0460a841313bbff68b8fd98a4", "signature": "c6a95fe7eab227efc1deab623691339532797cd0460a841313bbff68b8fd98a4"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts": {"version": "8a5e63aabf9ae9aaed6085e4af6bed45e5e85ce30d8ed1608c14f1b20b29b7ec", "signature": "8a5e63aabf9ae9aaed6085e4af6bed45e5e85ce30d8ed1608c14f1b20b29b7ec"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts": {"version": "178c66f9177264ae7368b75522233b1bde50162793dadad208d6d75ee5d843dc", "signature": "178c66f9177264ae7368b75522233b1bde50162793dadad208d6d75ee5d843dc"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts": {"version": "90dd4cdf0c9c33e5351725226288c33816cd748b5dad52cb50d94b441398024e", "signature": "90dd4cdf0c9c33e5351725226288c33816cd748b5dad52cb50d94b441398024e"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts": {"version": "4d270a5c575160d2c4a5e94b3dac821b8ca8ab618d2939c646f9d95eb2d10a31", "signature": "4d270a5c575160d2c4a5e94b3dac821b8ca8ab618d2939c646f9d95eb2d10a31"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts": {"version": "7d874cfe2d3ff47adc80c140a9dde3ede2bdf41876d64cffbf082d6415b16f39", "signature": "7d874cfe2d3ff47adc80c140a9dde3ede2bdf41876d64cffbf082d6415b16f39"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts": {"version": "3dc6b113e09568d20795a032237255a0cc0666132ad2c6bf7de475cc04d8d2da", "signature": "3dc6b113e09568d20795a032237255a0cc0666132ad2c6bf7de475cc04d8d2da"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts": {"version": "76939202c7b9bef1567fa37b619407c9b53dadd471f4b416a421d3fe08f457e0", "signature": "76939202c7b9bef1567fa37b619407c9b53dadd471f4b416a421d3fe08f457e0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts": {"version": "21cff650a0d60b4d4fdf21a830358edda3236ec7e6153f7d95ec9dbd92e9059c", "signature": "21cff650a0d60b4d4fdf21a830358edda3236ec7e6153f7d95ec9dbd92e9059c"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts": {"version": "827d7ccf768cde965966689a5283c01b0ccff5aa581c33320fc44fcfc00f9eea", "signature": "827d7ccf768cde965966689a5283c01b0ccff5aa581c33320fc44fcfc00f9eea"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts": {"version": "e69463fc48c0bf55977f35c729440ebccf6c9ee0321151a9f32299aaf3696440", "signature": "e69463fc48c0bf55977f35c729440ebccf6c9ee0321151a9f32299aaf3696440"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts": {"version": "d595b747c8eea8cbdf5fc2a45df609fa598a9e6f4de06780933157af87ec331d", "signature": "d595b747c8eea8cbdf5fc2a45df609fa598a9e6f4de06780933157af87ec331d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts": {"version": "b0d0df4c1efd16ba4dc38142107401fe57d544531a4cfe48ad7d21990f571c54", "signature": "b0d0df4c1efd16ba4dc38142107401fe57d544531a4cfe48ad7d21990f571c54"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts": {"version": "d5647918455ff4a74c3b4f98fc02a56b31e0023a4d77f6658aa87e916485f793", "signature": "d5647918455ff4a74c3b4f98fc02a56b31e0023a4d77f6658aa87e916485f793"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts": {"version": "5dbce8c3a4e2062b8d2605c2a7e88394bade12445d3d0391c9cee75f5a63f05d", "signature": "5dbce8c3a4e2062b8d2605c2a7e88394bade12445d3d0391c9cee75f5a63f05d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts": {"version": "119a23c2c978bf1d13ad8ed3adca5a713d7140ac01c498245daaa9271dcf3d5b", "signature": "119a23c2c978bf1d13ad8ed3adca5a713d7140ac01c498245daaa9271dcf3d5b"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts": {"version": "de844d34a76e27bed8ee823a1b46feebf0acd5e2e9737fdee87f52b523790e09", "signature": "de844d34a76e27bed8ee823a1b46feebf0acd5e2e9737fdee87f52b523790e09"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts": {"version": "4e3e5859b6592a4ccd270a5e93fed99ad09758c8e6396244376054e0e60fe5b8", "signature": "4e3e5859b6592a4ccd270a5e93fed99ad09758c8e6396244376054e0e60fe5b8"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts": {"version": "bf566fae3e5b7975036b91c4974c891eca505c7fe6ec1f438709c07a5e9b20d9", "signature": "bf566fae3e5b7975036b91c4974c891eca505c7fe6ec1f438709c07a5e9b20d9"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts": {"version": "15bb97585246dead03a754f3f7137fcd121cfd0ddb08e7f5e9db11d9bfe1d1b0", "signature": "15bb97585246dead03a754f3f7137fcd121cfd0ddb08e7f5e9db11d9bfe1d1b0"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts": {"version": "35ada29808981e035f58305eb900b6f566e699777372f60b4ab197185ce54c67", "signature": "35ada29808981e035f58305eb900b6f566e699777372f60b4ab197185ce54c67"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts": {"version": "cb5915e5a3da2786cff0dc3de85f03b048198e8b010f519566c426cf06bb051d", "signature": "cb5915e5a3da2786cff0dc3de85f03b048198e8b010f519566c426cf06bb051d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts": {"version": "8fd04d538962a689dbfe1f53b5d8245dd3afe03e2f53b878a721a65232bdf6e6", "signature": "8fd04d538962a689dbfe1f53b5d8245dd3afe03e2f53b878a721a65232bdf6e6"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts": {"version": "8d621f2eaf6126e62eb56ee1a9116e6dec7ab43d4f2146c39a046ac882976254", "signature": "8d621f2eaf6126e62eb56ee1a9116e6dec7ab43d4f2146c39a046ac882976254"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts": {"version": "f94f34edbcee30d4d62aa763f482d59fa232978d35ae196dd45ea65883f9b4df", "signature": "f94f34edbcee30d4d62aa763f482d59fa232978d35ae196dd45ea65883f9b4df"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts": {"version": "04e47b6301eb228df875add3ea94c4cd5ff448527056e677e72323d6bc0e52a1", "signature": "04e47b6301eb228df875add3ea94c4cd5ff448527056e677e72323d6bc0e52a1"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts": {"version": "89b404bc85198645ea12d2592de45c51249201e466a4ae79c9115657ead11b6f", "signature": "89b404bc85198645ea12d2592de45c51249201e466a4ae79c9115657ead11b6f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts": {"version": "2f09aa756aee85b1b16bed8dc409b475abc2096c0fe74c3bfa058bd7ed3b4731", "signature": "2f09aa756aee85b1b16bed8dc409b475abc2096c0fe74c3bfa058bd7ed3b4731"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts": {"version": "f131f173601d04a7dc218c53838c72cd4b5bc78fabc06f788c97fc2bd75e3ac2", "signature": "f131f173601d04a7dc218c53838c72cd4b5bc78fabc06f788c97fc2bd75e3ac2"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts": {"version": "e8a29f573c0a60e911d591db5d398fb4479fb94dca719ea7aa669db612c80e3a", "signature": "e8a29f573c0a60e911d591db5d398fb4479fb94dca719ea7aa669db612c80e3a"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts": {"version": "15bf2e6f073980b2d9cbc75147e76863b5b619941b3c6ae15b3384e671b6bd3b", "signature": "15bf2e6f073980b2d9cbc75147e76863b5b619941b3c6ae15b3384e671b6bd3b"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts": {"version": "3251fb1689e44258fe65a8081c3ddccf12aafe741f0459bd835cb1c30529a27f", "signature": "3251fb1689e44258fe65a8081c3ddccf12aafe741f0459bd835cb1c30529a27f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts": {"version": "c4fdbb256f278e73926cd9a3917d4f9ed7a7cd7487f532d83cd23f00c3771be3", "signature": "c4fdbb256f278e73926cd9a3917d4f9ed7a7cd7487f532d83cd23f00c3771be3"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts": {"version": "c88948c4fa6ac299332a8d025d47e188a2be6b3db33872b5d4e57041a4625df7", "signature": "c88948c4fa6ac299332a8d025d47e188a2be6b3db33872b5d4e57041a4625df7"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts": {"version": "1985d3e73fd606c764c73931400643ee0cc4d3f1267d0d4e1843c72b5dab5c26", "signature": "1985d3e73fd606c764c73931400643ee0cc4d3f1267d0d4e1843c72b5dab5c26"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts": {"version": "33f5f51d37c2639507e2581f45c0423b6af0fee369edbc3fac0a29c79bf051bb", "signature": "33f5f51d37c2639507e2581f45c0423b6af0fee369edbc3fac0a29c79bf051bb"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts": {"version": "5d87067866bb19ffa693fc2e4633f1f257abec8aab95d8bc5a5171fe4171b008", "signature": "5d87067866bb19ffa693fc2e4633f1f257abec8aab95d8bc5a5171fe4171b008"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts": {"version": "52aa0730e032102bee71fb7cf50c5337628c7f15233e4f66d0373f0cfe44b303", "signature": "52aa0730e032102bee71fb7cf50c5337628c7f15233e4f66d0373f0cfe44b303"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts": {"version": "d95dcb29b5dad217ecbf1795ef04f4a3edaa4f177d88d3a65122840aaa1a105f", "signature": "d95dcb29b5dad217ecbf1795ef04f4a3edaa4f177d88d3a65122840aaa1a105f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts": {"version": "f4553e051f230bed218bda9ffda0d6edb18cae53bd3a5458c298ea399aa9db44", "signature": "f4553e051f230bed218bda9ffda0d6edb18cae53bd3a5458c298ea399aa9db44"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts": {"version": "e458d822394bb6d98c462f8fbcd63a70ee115790b8ec983a129c0be9f7e71eba", "signature": "e458d822394bb6d98c462f8fbcd63a70ee115790b8ec983a129c0be9f7e71eba"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts": {"version": "74114725ebd52a8d29e2f5edf7e273b8c713cb27d5b0266b12a7be8cf96146f3", "signature": "74114725ebd52a8d29e2f5edf7e273b8c713cb27d5b0266b12a7be8cf96146f3"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts": {"version": "bf16e328650bdfaa442a050c5a85efbfd04cf2539e771bb704193ef30609b5db", "signature": "7a29ea0d3c8349394b6c833c2546f128864003dee9ad46ea6440314a5e741828"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts": {"version": "a7e047d59f4139171262849e0d2eac04a9b2f8ef6730d61fc977c2986cd8070d", "signature": "06db755d258e312564d690f34ef260ac8904723954b6cabdc9bc067144b12cc5"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts": {"version": "fb1cf51797e17db9546d8d3f8cfba424ac5574cf8aee5b7d5d2a9f782c2d4f7b", "signature": "094a8fb9b276c648011063b5f9aa55b216db5368767e034d56a8fe8ce69ab933"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts": {"version": "e51e353700a5f6f6a0184cca9e53dcf057bef0bae2bf8803ab45f0a342fb33aa", "signature": "ac3d39f6d2f2495ff4ae230823b083e447f3b1f0972e78d4155ee49a22763e55"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts": {"version": "86abf2ee3ef84a8d40947efc8141596e7b071239c3649bbe05c3b7cbf4ff25aa", "signature": "86abf2ee3ef84a8d40947efc8141596e7b071239c3649bbe05c3b7cbf4ff25aa"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts": {"version": "7e834906dceaaa112cc0ee0cbc277d0688cb2401d5d22d1cc8fd50982895507d", "signature": "7e834906dceaaa112cc0ee0cbc277d0688cb2401d5d22d1cc8fd50982895507d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts": {"version": "b97a55b37476e5d8a355ff53ce54d39d93e04326f286a28c72b1631bc489c7e4", "signature": "b97a55b37476e5d8a355ff53ce54d39d93e04326f286a28c72b1631bc489c7e4"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts": {"version": "644f7a7f1a6918d41a4549f24b73d8423512ef64bf00ede77c6f496429f653ce", "signature": "644f7a7f1a6918d41a4549f24b73d8423512ef64bf00ede77c6f496429f653ce"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts": {"version": "f4f347adb3c20b8d9fb4a091c290249d03e3691d5e4262dbeeaa9f14e0cd7124", "signature": "f4f347adb3c20b8d9fb4a091c290249d03e3691d5e4262dbeeaa9f14e0cd7124"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts": {"version": "1405854caa9cf70783113872afb1072091331d1bcdc01dd541389170f2f4e291", "signature": "1405854caa9cf70783113872afb1072091331d1bcdc01dd541389170f2f4e291"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts": {"version": "b7129b2d214aab183347c7212528591d7dcb1e3bf02a66205c4d137a6e7a375d", "signature": "b7129b2d214aab183347c7212528591d7dcb1e3bf02a66205c4d137a6e7a375d"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts": {"version": "134fce06b423352ff05e15abd8d25f766e0ebcc939a4b80bb5464b7c37795f0e", "signature": "134fce06b423352ff05e15abd8d25f766e0ebcc939a4b80bb5464b7c37795f0e"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts": {"version": "69edcc3a497cf02aba61a4a0e602786a8ae1dc2e650b54e89f8e19a75ef9d62f", "signature": "69edcc3a497cf02aba61a4a0e602786a8ae1dc2e650b54e89f8e19a75ef9d62f"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts": {"version": "b5e284217b0f1b760fbecd6e5bd72d8e7292d6d2a91131718a1617a7600895fc", "signature": "b5e284217b0f1b760fbecd6e5bd72d8e7292d6d2a91131718a1617a7600895fc"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts": {"version": "bbe60ef612ccc9b627648fd0d56e4e04c52bf670c25c4cd2b681cdbedfe16927", "signature": "bbe60ef612ccc9b627648fd0d56e4e04c52bf670c25c4cd2b681cdbedfe16927"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts": {"version": "bfb6158d32d15a5f518529d2941030215c6d6d31ba468faf805a427a9056f192", "signature": "bfb6158d32d15a5f518529d2941030215c6d6d31ba468faf805a427a9056f192"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts": {"version": "fbba802211f76068471429684b9d9929a075716e1d537dc04daf6b98ae71f387", "signature": "fbba802211f76068471429684b9d9929a075716e1d537dc04daf6b98ae71f387"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/invariant.ts": {"version": "a6c1bf33c9860b105b9880e700bdae4ff3a5e439656509f570131f193e26a1b7", "signature": "db5f7ccb8084d08dbb047c7c209f71b534b087260553330fbff3d74171fe7a80"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts": {"version": "8373cc91738a3f3cf5c8d33b47ca9493a229a818626d64960ac9db7d12f70187", "signature": "20b2e9562953b7d6e612f03863f64fba4c4ec10577830f2fb89d25e1efc4fbe6"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts": {"version": "662afc2590943a307ffb64b0bd895ac69c194e41ff69e5ce30930f27d1f6b015", "signature": "1332b9c26a7d3dfe802832b01756200f7cf0fe8195aeedc59e97ab30ceaa909a"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts": {"version": "e7f91307ee055529b6b539f53ff8c15820b2607886593eac1f8139ece95bca23", "signature": "4a36d82d466be0185cf98ec1b94bf508d70dd091e86983cf00d01a7ac0b733fe"}, "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/index.ts": {"version": "36cad1f05100d6cd7d5e566dfc65b13b7239a937517450ef009be3b0e69db244", "signature": "8483b40c3600d9fa0c5d0c6edee203dda33119bf04a96bcff40fa068a82553f8"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/events/index.d.ts": {"version": "400db42c3a46984118bff14260d60cec580057dc1ab4c2d7310beb643e4f5935", "signature": "400db42c3a46984118bff14260d60cec580057dc1ab4c2d7310beb643e4f5935"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts": {"version": "7e49dbf1543b3ee54853ade4c5e9fa460b6a4eca967efe6bf943e0c505d087ed", "signature": "7e49dbf1543b3ee54853ade4c5e9fa460b6a4eca967efe6bf943e0c505d087ed"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts": {"version": "a4108880c4f54e282bd9ed6f91ae6fbdc5f2cd8ed78bad8a38342403c0b65471", "signature": "a4108880c4f54e282bd9ed6f91ae6fbdc5f2cd8ed78bad8a38342403c0b65471"}, "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts": {"version": "1de0ff6200b92798a5aef43f57029c79dbf69932037dee1c007fdd2c562db258", "signature": "1de0ff6200b92798a5aef43f57029c79dbf69932037dee1c007fdd2c562db258"}}, "options": {"composite": true, "target": 4, "module": 1, "moduleResolution": 2, "esModuleInterop": true, "sourceMap": true, "declaration": true, "declarationMap": true, "removeComments": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": false, "noUnusedLocals": false, "forceConsistentCasingInFileNames": true, "lib": ["lib.es2017.d.ts", "lib.es2018.asynciterable.d.ts"], "types": ["node", "apollo-env"], "baseUrl": "/Users/<USER>/Desktop/Repos/apollo-tooling", "paths": {"*": ["types/*"]}, "rootDir": "/Users/<USER>/Desktop/Repos/apollo-tooling/packages/apollo-tools/src", "outDir": "/Users/<USER>/Desktop/Repos/apollo-tooling/packages/apollo-tools/lib", "configFilePath": "/Users/<USER>/Desktop/Repos/apollo-tooling/packages/apollo-tools/tsconfig.json"}, "referencedMap": {"/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es5.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.core.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.collection.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.generator.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.iterable.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.promise.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.proxy.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.reflect.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.array.include.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.object.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.string.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.intl.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2019.array.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/invariant.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/invariant.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/events/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts"]}, "exportedModulesMap": {"/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es5.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.core.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.collection.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.generator.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.iterable.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.promise.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.proxy.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.reflect.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.array.include.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.object.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.string.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.intl.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2019.array.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/invariant.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/events/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"], "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts"]}, "semanticDiagnosticsPerFile": ["/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es5.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.core.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.collection.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.generator.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.promise.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.object.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.string.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.intl.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/typescript/lib/lib.es2019.array.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/tsutils/maybe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/source.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/jsutils/promiseorvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/location.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/kinds.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/parser.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/printer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/directivelocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/directives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/schema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typeinfo.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/visitor.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/graphqlerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/syntaxerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/locatederror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/printerror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/formaterror.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/error/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/lexer.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/ast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/definition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/execute.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/graphql.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/scalars.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/introspection.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/type/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/values.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/execution/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/subscribe.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/subscription/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validationcontext.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/validate.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/executabledefinitions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueoperationnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneanonymousoperation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/singlefieldsubscriptions.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowntypenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fragmentsoncompositetypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesareinputtypes.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/scalarleafs.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/fieldsoncorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquefragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownfragmentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedfragments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/possiblefragmentspreads.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nofragmentcycles.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquevariablenames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/noundefinedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/nounusedvariables.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knowndirectives.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniquedirectivesperlocation.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/knownargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueargumentnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/valuesofcorrecttype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/providedrequiredarguments.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/variablesinallowedposition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/overlappingfieldscanbemerged.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/uniqueinputfieldnames.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/rules/loneschemadefinition.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/specifiedrules.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/validation/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionquery.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/getoperationroottype.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/introspectionfromschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildclientschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/language/blockstring.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/buildastschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/extendschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/lexicographicsortschema.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/schemaprinter.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/valuefromastuntyped.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/astfromvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/coercevalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidjsvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/isvalidliteralvalue.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/concatast.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/separateoperations.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/typecomparators.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/assertvalidname.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/findbreakingchanges.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/finddeprecatedusages.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/utilities/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/graphql/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/graphql.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolvermap.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/predicates.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/buildservicedefinition.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/array.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/object.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/polyfills/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/typescript-utility-types.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/fetch.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/url.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/fetch/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/createhash.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/isnodelike.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/mapvalues.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/predicates.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/utils/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-env/lib/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/invariant.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/utilities/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/resolveobject.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/schema/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/packages/apollo-tools/src/index.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/events/index.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/inspector.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/base.d.ts", "/users/trevorscheer/desktop/repos/apollo-tooling/node_modules/@types/node/ts3.2/index.d.ts"]}, "version": "3.5.3"}