{"name": "@apollographql/graphql-playground-html", "version": "1.6.24", "homepage": "https://github.com/graphcool/graphql-playground/tree/master/packages/graphql-playground-html", "description": "GraphQL IDE for better development workflows (GraphQL Subscriptions, interactive docs & collaboration).", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <johanne<PERSON>@graph.cool>", "<PERSON> <<EMAIL>>"], "repository": "http://github.com/graphcool/graphql-playground.git", "license": "MIT", "main": "dist/index.js", "files": ["dist"], "scripts": {"build": "rimraf dist && tsc", "prepare": "npm run build"}, "keywords": ["graphql", "graphiql", "playground", "graphcool"], "devDependencies": {"@types/node": "9.4.6", "rimraf": "2.6.2", "typescript": "2.6.2"}, "typings": "dist/index.d.ts", "typescript": {"definition": "dist/index.d.ts"}, "dependencies": {}}