{"version": 3, "names": ["whitespace", "require", "parens", "_t", "FLIPPED_ALIAS_KEYS", "VISITOR_KEYS", "isCallExpression", "isDecorator", "isExpressionStatement", "isMemberExpression", "isNewExpression", "isParenthesizedExpression", "TokenContext", "exports", "normal", "expressionStatement", "arrowBody", "exportDefault", "arrowFlowReturnType", "forInitHead", "forInHead", "forOfHead", "forInOrInitHeadAccumulate", "forInOrInitHeadAccumulatePassThroughMask", "expandAliases", "obj", "map", "Map", "add", "type", "func", "fn", "get", "set", "node", "parent", "stack", "getRawIdentifier", "_fn", "Object", "keys", "aliases", "alias", "expandedParens", "expandedWhitespaceNodes", "nodes", "isOrHasCallExpression", "object", "needsWhitespace", "_expandedWhitespaceNo", "expression", "flag", "needsWhitespaceBefore", "needsWhitespaceAfter", "needsParens", "tokenContext", "_expandedParens$get", "callee", "isDecoratorMemberExpression", "computed", "property", "isLastChild", "child", "visitorKeys", "i", "length", "val", "Array", "isArray", "j"], "sources": ["../../src/node/index.ts"], "sourcesContent": ["import * as whitespace from \"./whitespace.ts\";\nimport * as parens from \"./parentheses.ts\";\nimport {\n  FLIPPED_ALIAS_KEYS,\n  VISITOR_KEYS,\n  isCallExpression,\n  isDecorator,\n  isExpressionStatement,\n  isMemberExpression,\n  isNewExpression,\n  isParenthesizedExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { WhitespaceFlag } from \"./whitespace.ts\";\n\nexport const enum TokenContext {\n  normal = 0,\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  arrowFlowReturnType = 1 << 3,\n  forInitHead = 1 << 4,\n  forInHead = 1 << 5,\n  forOfHead = 1 << 6,\n  // This flag lives across the token boundary, and will\n  // be reset after forIn or forInit head is printed\n  forInOrInitHeadAccumulate = 1 << 7,\n  forInOrInitHeadAccumulatePassThroughMask = 0x80,\n}\n\ntype NodeHandler<R> = (\n  node: t.Node,\n  // todo:\n  // node: K extends keyof typeof t\n  //   ? Extract<typeof t[K], { type: \"string\" }>\n  //   : t.Node,\n  parent: t.Node,\n  tokenContext?: number,\n  getRawIdentifier?: (node: t.Identifier) => string,\n) => R;\n\nexport type NodeHandlers<R> = {\n  [K in string]?: NodeHandler<R>;\n};\n\nfunction expandAliases<R>(obj: NodeHandlers<R>) {\n  const map = new Map<string, NodeHandler<R>>();\n\n  function add(type: string, func: NodeHandler<R>) {\n    const fn = map.get(type);\n    map.set(\n      type,\n      fn\n        ? function (node, parent, stack, getRawIdentifier) {\n            return (\n              fn(node, parent, stack, getRawIdentifier) ??\n              func(node, parent, stack, getRawIdentifier)\n            );\n          }\n        : func,\n    );\n  }\n\n  for (const type of Object.keys(obj)) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      for (const alias of aliases) {\n        add(alias, obj[type]);\n      }\n    } else {\n      add(type, obj[type]);\n    }\n  }\n\n  return map;\n}\n\n// Rather than using `t.is` on each object property, we pre-expand any type aliases\n// into concrete types so that the 'find' call below can be as fast as possible.\nconst expandedParens = expandAliases(parens);\nconst expandedWhitespaceNodes = expandAliases(whitespace.nodes);\n\nfunction isOrHasCallExpression(node: t.Node): boolean {\n  if (isCallExpression(node)) {\n    return true;\n  }\n\n  return isMemberExpression(node) && isOrHasCallExpression(node.object);\n}\n\nexport function needsWhitespace(\n  node: t.Node,\n  parent: t.Node,\n  type: WhitespaceFlag,\n): boolean {\n  if (!node) return false;\n\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  const flag = expandedWhitespaceNodes.get(node.type)?.(node, parent);\n\n  if (typeof flag === \"number\") {\n    return (flag & type) !== 0;\n  }\n\n  return false;\n}\n\nexport function needsWhitespaceBefore(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 1);\n}\n\nexport function needsWhitespaceAfter(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 2);\n}\n\nexport function needsParens(\n  node: t.Node,\n  parent: t.Node,\n  tokenContext?: number,\n  getRawIdentifier?: (node: t.Identifier) => string,\n) {\n  if (!parent) return false;\n\n  if (isNewExpression(parent) && parent.callee === node) {\n    if (isOrHasCallExpression(node)) return true;\n  }\n\n  if (isDecorator(parent)) {\n    return (\n      !isDecoratorMemberExpression(node) &&\n      !(isCallExpression(node) && isDecoratorMemberExpression(node.callee)) &&\n      !isParenthesizedExpression(node)\n    );\n  }\n\n  return expandedParens.get(node.type)?.(\n    node,\n    parent,\n    tokenContext,\n    getRawIdentifier,\n  );\n}\n\nfunction isDecoratorMemberExpression(node: t.Node): boolean {\n  switch (node.type) {\n    case \"Identifier\":\n      return true;\n    case \"MemberExpression\":\n      return (\n        !node.computed &&\n        node.property.type === \"Identifier\" &&\n        isDecoratorMemberExpression(node.object)\n      );\n    default:\n      return false;\n  }\n}\n\nexport function isLastChild(parent: t.Node, child: t.Node) {\n  const visitorKeys = VISITOR_KEYS[parent.type];\n  for (let i = visitorKeys.length - 1; i >= 0; i--) {\n    const val = (parent as any)[visitorKeys[i]] as t.Node | t.Node[] | null;\n    if (val === child) {\n      return true;\n    } else if (Array.isArray(val)) {\n      let j = val.length - 1;\n      while (j >= 0 && val[j] === null) j--;\n      return j >= 0 && val[j] === child;\n    } else if (val) {\n      return false;\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,EAAA,GAAAF,OAAA;AASsB;EARpBG,kBAAkB;EAClBC,YAAY;EACZC,gBAAgB;EAChBC,WAAW;EACXC,qBAAqB;EACrBC,kBAAkB;EAClBC,eAAe;EACfC;AAAyB,IAAAR,EAAA;AAAA,MAMTS,YAAY,GAAAC,OAAA,CAAAD,YAAA;EAAAE,MAAA;EAAAC,mBAAA;EAAAC,SAAA;EAAAC,aAAA;EAAAC,mBAAA;EAAAC,WAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAC,yBAAA;EAAAC,wCAAA;AAAA;AA8B9B,SAASC,aAAaA,CAAIC,GAAoB,EAAE;EAC9C,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAyB,CAAC;EAE7C,SAASC,GAAGA,CAACC,IAAY,EAAEC,IAAoB,EAAE;IAC/C,MAAMC,EAAE,GAAGL,GAAG,CAACM,GAAG,CAACH,IAAI,CAAC;IACxBH,GAAG,CAACO,GAAG,CACLJ,IAAI,EACJE,EAAE,GACE,UAAUG,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,EAAE;MAAA,IAAAC,GAAA;MAC/C,QAAAA,GAAA,GACEP,EAAE,CAACG,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,CAAC,YAAAC,GAAA,GACzCR,IAAI,CAACI,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,gBAAgB,CAAC;IAE/C,CAAC,GACDP,IACN,CAAC;EACH;EAEA,KAAK,MAAMD,IAAI,IAAIU,MAAM,CAACC,IAAI,CAACf,GAAG,CAAC,EAAE;IACnC,MAAMgB,OAAO,GAAGrC,kBAAkB,CAACyB,IAAI,CAAC;IACxC,IAAIY,OAAO,EAAE;MACX,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QAC3Bb,GAAG,CAACc,KAAK,EAAEjB,GAAG,CAACI,IAAI,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLD,GAAG,CAACC,IAAI,EAAEJ,GAAG,CAACI,IAAI,CAAC,CAAC;IACtB;EACF;EAEA,OAAOH,GAAG;AACZ;AAIA,MAAMiB,cAAc,GAAGnB,aAAa,CAACtB,MAAM,CAAC;AAC5C,MAAM0C,uBAAuB,GAAGpB,aAAa,CAACxB,UAAU,CAAC6C,KAAK,CAAC;AAE/D,SAASC,qBAAqBA,CAACZ,IAAY,EAAW;EACpD,IAAI5B,gBAAgB,CAAC4B,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOzB,kBAAkB,CAACyB,IAAI,CAAC,IAAIY,qBAAqB,CAACZ,IAAI,CAACa,MAAM,CAAC;AACvE;AAEO,SAASC,eAAeA,CAC7Bd,IAAY,EACZC,MAAc,EACdN,IAAoB,EACX;EAAA,IAAAoB,qBAAA;EACT,IAAI,CAACf,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAI1B,qBAAqB,CAAC0B,IAAI,CAAC,EAAE;IAC/BA,IAAI,GAAGA,IAAI,CAACgB,UAAU;EACxB;EAEA,MAAMC,IAAI,IAAAF,qBAAA,GAAGL,uBAAuB,CAACZ,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAAtCoB,qBAAA,CAAyCf,IAAI,EAAEC,MAAM,CAAC;EAEnE,IAAI,OAAOgB,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,CAACA,IAAI,GAAGtB,IAAI,MAAM,CAAC;EAC5B;EAEA,OAAO,KAAK;AACd;AAEO,SAASuB,qBAAqBA,CAAClB,IAAY,EAAEC,MAAc,EAAE;EAClE,OAAOa,eAAe,CAACd,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASkB,oBAAoBA,CAACnB,IAAY,EAAEC,MAAc,EAAE;EACjE,OAAOa,eAAe,CAACd,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASmB,WAAWA,CACzBpB,IAAY,EACZC,MAAc,EACdoB,YAAqB,EACrBlB,gBAAiD,EACjD;EAAA,IAAAmB,mBAAA;EACA,IAAI,CAACrB,MAAM,EAAE,OAAO,KAAK;EAEzB,IAAIzB,eAAe,CAACyB,MAAM,CAAC,IAAIA,MAAM,CAACsB,MAAM,KAAKvB,IAAI,EAAE;IACrD,IAAIY,qBAAqB,CAACZ,IAAI,CAAC,EAAE,OAAO,IAAI;EAC9C;EAEA,IAAI3B,WAAW,CAAC4B,MAAM,CAAC,EAAE;IACvB,OACE,CAACuB,2BAA2B,CAACxB,IAAI,CAAC,IAClC,EAAE5B,gBAAgB,CAAC4B,IAAI,CAAC,IAAIwB,2BAA2B,CAACxB,IAAI,CAACuB,MAAM,CAAC,CAAC,IACrE,CAAC9C,yBAAyB,CAACuB,IAAI,CAAC;EAEpC;EAEA,QAAAsB,mBAAA,GAAOb,cAAc,CAACX,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAA7B2B,mBAAA,CACLtB,IAAI,EACJC,MAAM,EACNoB,YAAY,EACZlB,gBACF,CAAC;AACH;AAEA,SAASqB,2BAA2BA,CAACxB,IAAY,EAAW;EAC1D,QAAQA,IAAI,CAACL,IAAI;IACf,KAAK,YAAY;MACf,OAAO,IAAI;IACb,KAAK,kBAAkB;MACrB,OACE,CAACK,IAAI,CAACyB,QAAQ,IACdzB,IAAI,CAAC0B,QAAQ,CAAC/B,IAAI,KAAK,YAAY,IACnC6B,2BAA2B,CAACxB,IAAI,CAACa,MAAM,CAAC;IAE5C;MACE,OAAO,KAAK;EAChB;AACF;AAEO,SAASc,WAAWA,CAAC1B,MAAc,EAAE2B,KAAa,EAAE;EACzD,MAAMC,WAAW,GAAG1D,YAAY,CAAC8B,MAAM,CAACN,IAAI,CAAC;EAC7C,KAAK,IAAImC,CAAC,GAAGD,WAAW,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,MAAME,GAAG,GAAI/B,MAAM,CAAS4B,WAAW,CAACC,CAAC,CAAC,CAA6B;IACvE,IAAIE,GAAG,KAAKJ,KAAK,EAAE;MACjB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;MAC7B,IAAIG,CAAC,GAAGH,GAAG,CAACD,MAAM,GAAG,CAAC;MACtB,OAAOI,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACG,CAAC,CAAC,KAAK,IAAI,EAAEA,CAAC,EAAE;MACrC,OAAOA,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACG,CAAC,CAAC,KAAKP,KAAK;IACnC,CAAC,MAAM,IAAII,GAAG,EAAE;MACd,OAAO,KAAK;IACd;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}